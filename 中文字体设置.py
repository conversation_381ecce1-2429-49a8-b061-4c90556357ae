"""
中文字体设置工具
检测和配置matplotlib的中文字体支持
"""

import matplotlib.pyplot as plt
import matplotlib
from matplotlib.font_manager import FontProperties, findfont, FontManager
import os
import platform

def detect_chinese_fonts():
    """检测系统中可用的中文字体"""
    print("🔍 检测系统中的中文字体...")
    
    # 常见的中文字体名称
    chinese_fonts = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',           # 宋体
        'KaiTi',            # 楷体
        'FangSong',         # 仿宋
        'STSong',           # 华文宋体
        'STKaiti',          # 华文楷体
        'STHeiti',          # 华文黑体
        'Arial Unicode MS', # Arial Unicode MS
        'PingFang SC',      # 苹果系统字体
        'Hiragino Sans GB', # 苹果系统字体
        'WenQuanYi Micro Hei', # Linux字体
        'Noto Sans CJK SC'  # Google字体
    ]
    
    available_fonts = []
    fm = FontManager()
    
    for font_name in chinese_fonts:
        try:
            # 尝试查找字体
            font_path = findfont(FontProperties(family=font_name))
            if font_path and os.path.exists(font_path):
                available_fonts.append(font_name)
                print(f"✅ 找到字体: {font_name}")
        except Exception as e:
            print(f"❌ 字体不可用: {font_name}")
    
    return available_fonts

def test_chinese_display(font_name=None):
    """测试中文显示效果"""
    print(f"\n🧪 测试中文显示效果...")
    
    if font_name:
        plt.rcParams['font.sans-serif'] = [font_name]
        print(f"使用字体: {font_name}")
    
    try:
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 测试各种中文文本
        test_texts = [
            "LFW数据集样本展示",
            "数据集统计信息", 
            "特征脸可视化",
            "PCA降维效果分析",
            "模型性能对比分析",
            "混淆矩阵热力图"
        ]
        
        for i, text in enumerate(test_texts):
            ax.text(0.1, 0.9 - i*0.15, text, fontsize=14, transform=ax.transAxes)
        
        ax.set_title("中文字体显示测试", fontsize=16)
        ax.set_xlabel("横轴标签")
        ax.set_ylabel("纵轴标签")
        ax.axis('off')
        
        plt.tight_layout()
        plt.savefig('中文字体测试.png', dpi=150, bbox_inches='tight')
        plt.show()
        
        print("✅ 中文显示测试完成，已保存为 '中文字体测试.png'")
        return True
        
    except Exception as e:
        print(f"❌ 中文显示测试失败: {e}")
        return False

def configure_chinese_fonts():
    """配置中文字体"""
    print("⚙️ 配置matplotlib中文字体...")
    
    # 检测可用字体
    available_fonts = detect_chinese_fonts()
    
    if not available_fonts:
        print("❌ 未找到任何中文字体！")
        print("建议安装以下字体之一:")
        print("- Windows: 系统自带SimHei、Microsoft YaHei")
        print("- macOS: 系统自带PingFang SC")
        print("- Linux: 安装 fonts-wqy-microhei 或 fonts-noto-cjk")
        return False
    
    # 选择最佳字体
    preferred_fonts = ['Microsoft YaHei', 'SimHei', 'PingFang SC', 'Hiragino Sans GB']
    best_font = None
    
    for font in preferred_fonts:
        if font in available_fonts:
            best_font = font
            break
    
    if not best_font:
        best_font = available_fonts[0]
    
    print(f"🎯 选择字体: {best_font}")
    
    # 设置matplotlib参数
    matplotlib.rcParams['font.sans-serif'] = [best_font] + available_fonts
    matplotlib.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.sans-serif'] = [best_font] + available_fonts
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = ['sans-serif']
    
    # 测试设置效果
    success = test_chinese_display(best_font)
    
    if success:
        print("✅ 中文字体配置成功！")
        return best_font
    else:
        print("❌ 中文字体配置失败！")
        return None

def create_font_config_code(font_name):
    """生成字体配置代码"""
    config_code = f'''
# 中文字体配置代码
import matplotlib.pyplot as plt
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['{font_name}', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['{font_name}', 'Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = ['sans-serif']

print("✅ 中文字体设置完成: {font_name}")
'''
    
    with open("字体配置代码.py", "w", encoding="utf-8") as f:
        f.write(config_code)
    
    print("📄 字体配置代码已保存到: 字体配置代码.py")

def install_font_guide():
    """显示字体安装指南"""
    print("\n📖 字体安装指南:")
    print("=" * 50)
    
    system = platform.system()
    
    if system == "Windows":
        print("Windows系统:")
        print("1. 系统通常自带SimHei(黑体)和Microsoft YaHei(微软雅黑)")
        print("2. 如果没有，可以从控制面板 > 字体 中安装")
        print("3. 或者下载字体文件放到 C:\\Windows\\Fonts\\ 目录")
        
    elif system == "Darwin":  # macOS
        print("macOS系统:")
        print("1. 系统自带PingFang SC和Hiragino Sans GB")
        print("2. 可以从字体册应用中安装新字体")
        print("3. 或者将字体文件拖拽到字体册中")
        
    elif system == "Linux":
        print("Linux系统:")
        print("1. Ubuntu/Debian: sudo apt-get install fonts-wqy-microhei")
        print("2. CentOS/RHEL: sudo yum install wqy-microhei-fonts")
        print("3. 或者安装Google Noto字体: sudo apt-get install fonts-noto-cjk")
        
    else:
        print("其他系统:")
        print("请根据系统文档安装中文字体")

def main():
    """主函数"""
    print("=" * 60)
    print("matplotlib中文字体配置工具")
    print("=" * 60)
    
    # 显示系统信息
    print(f"操作系统: {platform.system()}")
    print(f"Python版本: {platform.python_version()}")
    print(f"matplotlib版本: {matplotlib.__version__}")
    
    # 配置字体
    best_font = configure_chinese_fonts()
    
    if best_font:
        # 生成配置代码
        create_font_config_code(best_font)
        
        print("\n🎉 配置完成！")
        print("现在可以在其他程序中使用中文字体了。")
        print("如果其他程序仍显示方块，请在程序开头添加:")
        print("exec(open('字体配置代码.py').read())")
        
    else:
        print("\n❌ 字体配置失败！")
        install_font_guide()
        
        print("\n💡 临时解决方案:")
        print("如果无法安装中文字体，可以:")
        print("1. 将图表标题改为英文")
        print("2. 使用图片编辑软件后期添加中文标签")
        print("3. 在论文中用英文图表+中文说明")

if __name__ == "__main__":
    main()
