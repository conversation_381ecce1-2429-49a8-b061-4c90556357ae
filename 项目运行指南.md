# 基于LFW数据集的人脸识别系统 - 项目运行指南

## 项目概述

本项目实现了基于LFW数据集的人脸识别系统，包含完整的论文和代码实现。项目包含以下主要文件：

### 核心文件
1. **基于LFW数据集的人脸识别系统论文.md** - 完整的期末论文
2. **完整的人脸识别系统.py** - 主要的系统实现代码
3. **论文图表生成器.py** - 专门用于生成论文图表的脚本
4. **项目运行指南.md** - 本文件，项目使用说明

### 原始代码文件（已改进）
- **face_recognition.py** - 基础SVM人脸识别实现
- **face_recognition_Adaboost.py** - AdaBoost集成方法实现
- **face_recognition_other_ensemble.py** - 其他集成方法实现

## 环境配置

### 必需的Python包
```bash
pip install numpy
pip install matplotlib
pip install seaborn
pip install scikit-learn
pip install pandas
```

### 推荐的Python版本
- Python 3.7 或更高版本

### 开发环境
- PyCharm（推荐）
- Jupyter Notebook
- VS Code

## 运行步骤

### 第零步：验证本地数据集（推荐）

首先验证您下载的本地数据集是否正确配置：

```bash
python 验证本地数据.py
```

这将会：
1. 检查数据文件夹结构是否正确
2. 测试数据加载是否成功
3. 显示数据集基本信息
4. 生成验证报告

### 第一步：生成论文图表（推荐使用强制中文版）

**推荐使用强制中文字体版本**，确保图表中文正常显示：

```bash
python 论文图表生成器_强制中文版.py
```

或者使用原版本（如果中文显示有问题）：

```bash
python 论文图表生成器.py
```

这将会：
1. **强制设置中文字体**，确保图表中文正常显示
2. **使用您的本地数据集**（从./data文件夹加载）
3. 生成6个主要图表
4. 将图表保存到"论文图表"文件夹
5. 创建图表说明文档

**预期输出：**
- 图1: LFW数据集样本展示
- 图2: 数据集统计信息
- 图3: 特征脸可视化
- 图4: PCA降维效果分析
- 图5: 模型性能对比分析
- 图6: 最佳模型混淆矩阵

### 第二步：运行完整系统分析（推荐使用强制中文版）

**推荐使用强制中文字体版本**：

```bash
python 完整的人脸识别系统_强制中文版.py
```

或者使用原版本：

```bash
python 完整的人脸识别系统.py
```

这将会：
1. **强制设置中文字体**，确保所有图表中文正常显示
2. 加载和预处理数据
3. 进行PCA特征提取
4. 训练多种分类器
5. 评估模型性能
6. 生成性能对比报告
7. 显示可视化结果

### 第三步：运行原始代码（可选）

如果需要运行原始的代码文件：

```bash
# 基础SVM实现
python Lfw_face_recognition_svm_ensemble-master/face_recognition.py

# AdaBoost实现
python Lfw_face_recognition_svm_ensemble-master/face_recognition_Adaboost.py

# 其他集成方法
python Lfw_face_recognition_svm_ensemble-master/face_recognition_other_ensemble.py
```

## 论文完成指南

### 1. 论文结构
论文已按照模板要求完成，包含：
- 摘要和关键词
- 第一章：引言
- 第二章：数据预处理
- 第三章：模型构建
- 第四章：模型评估
- 第五章：总结与展望
- 参考文献

### 2. 图表插入位置
论文中已标注了10个图表插入位置：

**【图片插入位置1：LFW数据集样本展示】**
- 文件：图1_LFW数据集样本展示.png
- 位置：第二章 2.1 数据分析

**【图片插入位置2：数据集统计信息图表】**
- 文件：图2_数据集统计信息.png
- 位置：第二章 2.1 数据分析

**【图片插入位置3：特征脸可视化】**
- 文件：图3_特征脸可视化.png
- 位置：第二章 2.3 特征提取

**【图片插入位置4：降维前后特征维度对比】**
- 文件：图4_PCA降维效果分析.png
- 位置：第二章 2.4 数据可视化

**【图片插入位置5：SVM分类结果展示】**
- 文件：图5_模型性能对比.png（部分）
- 位置：第四章 4.1 模型训练结果

**【图片插入位置6：AdaBoost算法性能比较图】**
- 可从原始代码运行结果获取
- 位置：第四章 4.1 模型训练结果

**【图片插入位置7：AdaBoost训练过程可视化】**
- 可从原始代码运行结果获取
- 位置：第四章 4.1 模型训练结果

**【图片插入位置8：多种算法性能对比表】**
- 文件：图5_模型性能对比.png
- 位置：第四章 4.1 模型训练结果

**【图片插入位置9：混淆矩阵热力图】**
- 文件：图6_最佳模型混淆矩阵.png
- 位置：第四章 4.2 关键指标分析

**【图片插入位置10：算法训练时间对比图】**
- 文件：图5_模型性能对比.png（部分）
- 位置：第四章 4.2 关键指标分析

### 3. 个人信息填写
请在论文中填写以下信息：
- 姓名
- 学号
- 完成时间

## 预期结果

### 性能指标
根据实验设计，预期各算法性能如下：
- SVM: 准确率约0.80-0.85
- Random Forest: 准确率约0.75-0.80
- AdaBoost: 准确率约0.70-0.75
- Gradient Boosting: 准确率约0.75-0.80
- KNN: 准确率约0.65-0.70

### 运行时间
- 数据加载：约10-30秒（首次下载数据集）
- PCA特征提取：约5-10秒
- 模型训练：约30-60秒（所有模型）
- 图表生成：约20-30秒

## 常见问题解决

### 1. 数据集下载失败
如果LFW数据集下载失败，可以：
- 检查网络连接
- 使用VPN
- 手动下载数据集到sklearn数据目录

### 2. 内存不足
如果运行时内存不足：
- 减少`min_faces_per_person`参数
- 减少PCA组件数量
- 使用更小的`resize`参数

### 3. 图表显示问题
如果图表无法正常显示：
- 安装中文字体支持
- 检查matplotlib后端设置
- 使用Jupyter Notebook运行

### 4. 包导入错误
确保安装了所有必需的包：
```bash
pip install -r requirements.txt
```

## 提交清单

最终提交应包含：
1. ✅ 完整的论文文档（Markdown或Word格式）
2. ✅ 所有Python源代码文件
3. ✅ 生成的图表文件
4. ✅ 项目运行指南
5. ✅ 实验结果截图

## 评分要点

根据作业要求，重点关注：
1. **代码规范性**：注释完整，结构清晰
2. **算法实现**：正确实现多种机器学习算法
3. **实验设计**：完整的数据挖掘流程
4. **结果分析**：深入的性能分析和比较
5. **论文质量**：格式规范，内容完整
6. **创新性**：算法改进或应用创新

## 联系信息

如有问题，请联系：
- 作者：[您的姓名]
- 邮箱：[您的邮箱]
- 课程：非结构化数据挖掘

---

**祝您顺利完成期末作业！** 🎓
