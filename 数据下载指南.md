# LFW数据集下载指南

## 数据集概述

本项目使用的是**LFW (Labeled Faces in the Wild)数据集**，这是人脸识别领域的标准测试数据集。

### 数据集信息
- **名称：** Labeled Faces in the Wild (LFW)
- **大小：** 约233MB
- **图像数量：** 13,233张人脸图像
- **人物数量：** 5,749个不同的人物
- **官方网站：** http://vis-www.cs.umass.edu/lfw/

## 方式一：自动下载（推荐）⭐

### 优点
- 无需手动操作
- sklearn自动处理数据格式
- 代码运行时自动下载

### 使用方法
直接运行代码，sklearn会自动下载：
```python
from sklearn.datasets import fetch_lfw_people
lfw_people = fetch_lfw_people(min_faces_per_person=70, resize=0.4)
```

### 自动下载位置
- **Windows:** `C:\Users\<USER>\scikit_learn_data\lfw_home\`
- **Linux/Mac:** `~/scikit_learn_data/lfw_home/`

### 如果自动下载成功
运行代码时会看到：
```
✅ 数据集自动下载成功！
```

## 方式二：手动下载（备用方案）

### 何时需要手动下载
- 网络连接问题导致自动下载失败
- 公司/学校网络限制
- 需要使用代理服务器

### 手动下载步骤

#### 1. 下载数据文件
访问以下任一链接下载：

**主要下载链接：**
```
http://vis-www.cs.umass.edu/lfw/lfw-funneled.tgz
```

**备用下载链接：**
```
http://vis-www.cs.umass.edu/lfw/lfw.tgz
```

#### 2. 创建数据目录
在项目根目录创建以下文件夹结构：
```
项目根目录/
├── data/
│   └── lfw_home/
│       └── lfw_funneled/
```

#### 3. 解压数据文件
将下载的 `lfw-funneled.tgz` 解压到 `data/lfw_home/` 目录下

#### 4. 修改代码以使用本地数据
创建一个新的数据加载函数：

```python
import os
from sklearn.datasets import fetch_lfw_people

def load_local_lfw_data():
    """从本地加载LFW数据"""
    data_home = "./data"
    
    if os.path.exists(data_home):
        return fetch_lfw_people(
            min_faces_per_person=70, 
            resize=0.4,
            data_home=data_home
        )
    else:
        # 如果本地没有数据，使用自动下载
        return fetch_lfw_people(min_faces_per_person=70, resize=0.4)
```

## 方式三：使用镜像源（中国用户推荐）

### 如果您在中国大陆
可以使用以下镜像源加速下载：

#### 1. 设置环境变量
```bash
# Windows (命令提示符)
set SKLEARN_DATA_URL=https://mirrors.tuna.tsinghua.edu.cn/

# Linux/Mac (终端)
export SKLEARN_DATA_URL=https://mirrors.tuna.tsinghua.edu.cn/
```

#### 2. 或者使用代理
```python
import os
os.environ['http_proxy'] = 'http://your-proxy:port'
os.environ['https_proxy'] = 'http://your-proxy:port'
```

## 验证数据下载

### 运行测试代码
创建一个测试文件 `test_data.py`：

```python
from sklearn.datasets import fetch_lfw_people
import numpy as np

def test_lfw_data():
    """测试LFW数据集是否正确加载"""
    try:
        print("正在测试LFW数据集...")
        lfw_people = fetch_lfw_people(min_faces_per_person=70, resize=0.4)
        
        n_samples, h, w = lfw_people.images.shape
        X = lfw_people.data
        y = lfw_people.target
        target_names = lfw_people.target_names
        
        print("✅ 数据集加载成功！")
        print(f"样本总数: {n_samples}")
        print(f"图像尺寸: {h} x {w}")
        print(f"特征维度: {X.shape[1]}")
        print(f"类别数量: {len(target_names)}")
        print(f"人物列表: {target_names}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集加载失败: {e}")
        return False

if __name__ == "__main__":
    test_lfw_data()
```

### 预期输出
如果数据加载成功，您应该看到类似输出：
```
✅ 数据集加载成功！
样本总数: 1288
图像尺寸: 50 x 37
特征维度: 1850
类别数量: 7
人物列表: ['Ariel Sharon' 'Colin Powell' 'Donald Rumsfeld' 'George W Bush'
 'Gerhard Schroeder' 'Hugo Chavez' 'Tony Blair']
```

## 常见问题解决

### 1. 下载速度慢
**解决方案：**
- 使用VPN或代理
- 选择网络较好的时间段
- 使用手动下载方式

### 2. SSL证书错误
**解决方案：**
```python
import ssl
ssl._create_default_https_context = ssl._create_unverified_context
```

### 3. 权限错误
**解决方案：**
- 确保有写入权限
- 在管理员模式下运行
- 检查防火墙设置

### 4. 磁盘空间不足
**解决方案：**
- 清理磁盘空间（至少需要500MB）
- 选择其他磁盘位置

### 5. 网络连接超时
**解决方案：**
```python
import socket
socket.setdefaulttimeout(300)  # 设置5分钟超时
```

## 数据集文件结构

下载完成后，数据集的文件结构应该是：
```
scikit_learn_data/
└── lfw_home/
    ├── lfw_funneled/
    │   ├── Ariel_Sharon/
    │   ├── Colin_Powell/
    │   ├── Donald_Rumsfeld/
    │   ├── George_W_Bush/
    │   ├── Gerhard_Schroeder/
    │   ├── Hugo_Chavez/
    │   └── Tony_Blair/
    └── lfw_funneled.tgz
```

## 推荐方案

### 对于大多数用户
1. **首先尝试自动下载**（方式一）
2. 如果失败，使用手动下载（方式二）

### 对于中国大陆用户
1. 设置镜像源（方式三）
2. 或使用VPN后自动下载

### 对于企业/学校网络
1. 手动下载到本地（方式二）
2. 配置代理服务器

## 注意事项

1. **首次运行时间较长**：数据下载和处理需要几分钟
2. **网络要求**：需要稳定的网络连接
3. **存储空间**：确保有足够的磁盘空间
4. **Python版本**：建议使用Python 3.7+

## 联系支持

如果遇到数据下载问题，可以：
1. 查看sklearn官方文档
2. 检查网络连接
3. 尝试不同的下载方式
4. 联系技术支持

---

**建议：先尝试自动下载，如果失败再考虑手动下载。** 🚀
