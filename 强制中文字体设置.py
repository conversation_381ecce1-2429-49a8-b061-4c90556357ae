"""
强制中文字体设置
彻底解决matplotlib中文显示问题
"""

import matplotlib
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import platform
import warnings

def force_chinese_font_setup():
    """强制设置中文字体"""
    print("🔧 强制设置中文字体...")
    
    # 清除matplotlib字体缓存
    try:
        cache_dir = matplotlib.get_cachedir()
        font_cache = os.path.join(cache_dir, 'fontlist-v330.json')
        if os.path.exists(font_cache):
            os.remove(font_cache)
            print("✅ 清除字体缓存")
    except:
        pass
    
    # 重新加载字体管理器
    matplotlib.font_manager._rebuild()
    
    # 获取系统所有字体
    font_list = [f.name for f in fm.fontManager.ttflist]
    print(f"📋 系统共有 {len(font_list)} 个字体")
    
    # 查找中文字体
    chinese_fonts_found = []
    chinese_font_patterns = [
        'SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong',
        'STHeiti', 'STSong', 'STKaiti', 'STFangsong',
        'PingFang SC', 'Hiragino Sans GB', 'Apple LiGothic',
        'WenQuanYi', 'Noto Sans CJK', 'Source Han Sans',
        'Arial Unicode MS', 'Lucida Sans Unicode'
    ]
    
    for font in font_list:
        for pattern in chinese_font_patterns:
            if pattern.lower() in font.lower():
                chinese_fonts_found.append(font)
                print(f"✅ 找到中文字体: {font}")
                break
    
    if not chinese_fonts_found:
        print("❌ 未找到任何中文字体！")
        # 使用系统默认字体
        if platform.system() == "Windows":
            chinese_fonts_found = ['SimHei', 'Microsoft YaHei', 'SimSun']
        elif platform.system() == "Darwin":
            chinese_fonts_found = ['PingFang SC', 'Hiragino Sans GB']
        else:
            chinese_fonts_found = ['DejaVu Sans']
    
    # 强制设置字体
    font_list_final = chinese_fonts_found + ['DejaVu Sans', 'Arial', 'sans-serif']
    
    # 设置matplotlib全局参数
    plt.rcParams.update({
        'font.sans-serif': font_list_final,
        'font.family': 'sans-serif',
        'axes.unicode_minus': False,
        'font.size': 12
    })
    
    matplotlib.rcParams.update({
        'font.sans-serif': font_list_final,
        'font.family': 'sans-serif', 
        'axes.unicode_minus': False,
        'font.size': 12
    })
    
    print(f"🎯 设置字体优先级: {font_list_final[:3]}")
    
    return chinese_fonts_found[0] if chinese_fonts_found else 'DejaVu Sans'

def test_chinese_display_comprehensive():
    """全面测试中文显示"""
    print("\n🧪 全面测试中文显示...")
    
    # 测试文本
    test_texts = [
        "LFW数据集样本展示",
        "数据集统计信息", 
        "特征脸可视化",
        "PCA降维效果分析",
        "模型性能对比分析",
        "混淆矩阵热力图",
        "准确率", "精确率", "召回率", "F1分数"
    ]
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle("中文字体显示全面测试", fontsize=16, fontweight='bold')
    
    # 测试1: 标题和标签
    ax1 = axes[0, 0]
    ax1.plot([1, 2, 3], [1, 4, 2])
    ax1.set_title("图表标题测试")
    ax1.set_xlabel("横轴标签")
    ax1.set_ylabel("纵轴标签")
    
    # 测试2: 文本列表
    ax2 = axes[0, 1]
    for i, text in enumerate(test_texts[:5]):
        ax2.text(0.1, 0.9 - i*0.18, f"{i+1}. {text}", 
                transform=ax2.transAxes, fontsize=10)
    ax2.set_title("文本列表测试")
    ax2.axis('off')
    
    # 测试3: 图例
    ax3 = axes[1, 0]
    ax3.plot([1, 2, 3], [1, 2, 3], label="数据系列1")
    ax3.plot([1, 2, 3], [3, 2, 1], label="数据系列2")
    ax3.legend()
    ax3.set_title("图例测试")
    
    # 测试4: 注释
    ax4 = axes[1, 1]
    ax4.scatter([1, 2, 3], [1, 4, 2])
    ax4.annotate("重要数据点", xy=(2, 4), xytext=(2.5, 3.5),
                arrowprops=dict(arrowstyle='->'))
    ax4.set_title("注释测试")
    
    plt.tight_layout()
    plt.savefig('中文字体全面测试.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 中文显示测试完成，结果保存为 '中文字体全面测试.png'")
    print("请检查图片中的中文是否正常显示")

def create_font_fix_code():
    """创建字体修复代码"""
    fix_code = '''
# 强制中文字体设置代码 - 复制到其他程序开头使用

import matplotlib
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import warnings

# 忽略字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

# 强制设置中文字体
def setup_chinese_font():
    # 获取系统字体
    font_list = [f.name for f in fm.fontManager.ttflist]
    
    # 中文字体候选
    chinese_fonts = []
    for font in font_list:
        if any(pattern in font for pattern in ['SimHei', 'Microsoft YaHei', 'SimSun', 'PingFang', 'Hiragino']):
            chinese_fonts.append(font)
    
    # 设置字体
    font_priority = chinese_fonts + ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial']
    
    plt.rcParams['font.sans-serif'] = font_priority
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['axes.unicode_minus'] = False
    
    matplotlib.rcParams['font.sans-serif'] = font_priority
    matplotlib.rcParams['font.family'] = 'sans-serif'
    matplotlib.rcParams['axes.unicode_minus'] = False

# 执行字体设置
setup_chinese_font()
print("✅ 中文字体强制设置完成")
'''
    
    with open("字体强制修复代码.py", "w", encoding="utf-8") as f:
        f.write(fix_code)
    
    print("📄 字体修复代码已保存到: 字体强制修复代码.py")

def main():
    """主函数"""
    print("=" * 60)
    print("强制中文字体设置工具")
    print("=" * 60)
    
    # 显示系统信息
    print(f"操作系统: {platform.system()}")
    print(f"matplotlib版本: {matplotlib.__version__}")
    
    # 强制设置字体
    best_font = force_chinese_font_setup()
    
    # 测试显示效果
    test_chinese_display_comprehensive()
    
    # 创建修复代码
    create_font_fix_code()
    
    print("\n" + "=" * 60)
    print("🎉 字体设置完成！")
    print("=" * 60)
    print("如果测试图片中的中文正常显示，说明设置成功。")
    print("如果仍有问题，请在其他程序开头添加:")
    print("exec(open('字体强制修复代码.py').read())")
    
    print(f"\n推荐字体: {best_font}")

if __name__ == "__main__":
    main()
