"""
论文图表生成器
用于生成非结构化数据挖掘期末论文所需的所有图表
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.datasets import fetch_lfw_people
from sklearn.model_selection import train_test_split
from sklearn.decomposition import PCA
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier, AdaBoostClassifier, GradientBoostingClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import confusion_matrix, accuracy_score
import pandas as pd
from time import time
import os

# 设置中文字体和图表样式
import matplotlib
from matplotlib.font_manager import FontProperties

def setup_chinese_fonts():
    """设置中文字体"""
    # 常见中文字体列表
    chinese_fonts = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',           # 宋体
        'KaiTi',            # 楷体
        'STHeiti',          # 华文黑体
        'PingFang SC',      # 苹果字体
        'Hiragino Sans GB', # 苹果字体
        'WenQuanYi Micro Hei', # Linux字体
        'Arial Unicode MS', # 通用字体
        'DejaVu Sans'       # 备用字体
    ]

    # 设置matplotlib参数
    matplotlib.rcParams['font.sans-serif'] = chinese_fonts
    matplotlib.rcParams['axes.unicode_minus'] = False
    matplotlib.rcParams['font.family'] = ['sans-serif']

    plt.rcParams['font.sans-serif'] = chinese_fonts
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = ['sans-serif']

    # 测试中文显示
    try:
        fig, ax = plt.subplots(figsize=(1, 1))
        ax.text(0.5, 0.5, '测试中文', ha='center', va='center', fontsize=12)
        plt.close(fig)
        print("✅ 中文字体设置成功")
        return True
    except Exception as e:
        print(f"⚠️ 中文字体设置可能有问题: {e}")
        print("图表中的中文可能显示为方块")
        return False

# 设置中文字体
setup_chinese_fonts()

# 设置图表样式
try:
    plt.style.use('seaborn-v0_8')
except:
    try:
        plt.style.use('seaborn')
    except:
        print("⚠️ seaborn样式不可用，使用默认样式")

class PaperFigureGenerator:
    """论文图表生成器"""
    
    def __init__(self):
        self.output_dir = "论文图表"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("加载LFW数据集...")

        # 使用本地数据集
        local_data_home = "./data"

        try:
            # 首先尝试从本地加载
            if os.path.exists(local_data_home):
                print(f"📁 使用本地数据集: {local_data_home}")
                self.lfw_people = fetch_lfw_people(
                    min_faces_per_person=70,
                    resize=0.4,
                    data_home=local_data_home
                )
                print("✅ 本地数据集加载成功！")
            else:
                # 如果本地没有数据，使用自动下载
                print("⚠️  本地数据集不存在，尝试自动下载...")
                self.lfw_people = fetch_lfw_people(min_faces_per_person=70, resize=0.4)
                print("✅ 数据集自动下载成功！")

        except Exception as e:
            print(f"❌ 数据集加载失败: {e}")
            print("请检查数据集是否正确放置在 './data' 文件夹中")
            raise
        
        self.n_samples, self.h, self.w = self.lfw_people.images.shape
        self.X = self.lfw_people.data
        self.y = self.lfw_people.target
        self.target_names = self.lfw_people.target_names
        self.n_classes = len(self.target_names)
        
        # 划分数据集
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            self.X, self.y, test_size=0.25, random_state=42, stratify=self.y
        )
        
        # PCA特征提取
        self.pca = PCA(n_components=150, svd_solver='randomized', whiten=True)
        self.X_train_pca = self.pca.fit_transform(self.X_train)
        self.X_test_pca = self.pca.transform(self.X_test)
        self.eigenfaces = self.pca.components_.reshape((150, self.h, self.w))
        
    def generate_figure1_dataset_samples(self):
        """图1: LFW数据集样本展示"""
        fig, axes = plt.subplots(3, 4, figsize=(12, 9))
        fig.suptitle('图1: LFW数据集样本展示', fontsize=16, fontweight='bold')
        
        # 选择不同人物的代表性图像
        selected_indices = []
        used_people = set()
        
        for i in range(len(self.y)):
            if self.y[i] not in used_people and len(selected_indices) < 12:
                selected_indices.append(i)
                used_people.add(self.y[i])
                
        for idx, img_idx in enumerate(selected_indices):
            row, col = idx // 4, idx % 4
            axes[row, col].imshow(self.lfw_people.images[img_idx], cmap='gray')
            axes[row, col].set_title(f'{self.target_names[self.y[img_idx]]}', 
                                   fontsize=10, fontweight='bold')
            axes[row, col].axis('off')
            
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图1_LFW数据集样本展示.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_figure2_dataset_statistics(self):
        """图2: 数据集统计信息"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 左图：各人物样本数量分布
        person_counts = [np.sum(self.y == i) for i in range(self.n_classes)]
        ax1.bar(range(self.n_classes), person_counts, color='skyblue', alpha=0.7)
        ax1.set_xlabel('人物编号')
        ax1.set_ylabel('样本数量')
        ax1.set_title('各人物样本数量分布')
        ax1.grid(True, alpha=0.3)
        
        # 右图：数据集基本信息
        info_data = {
            '总样本数': self.n_samples,
            '特征维度': self.X.shape[1],
            '类别数量': self.n_classes,
            '训练集大小': len(self.X_train),
            '测试集大小': len(self.X_test)
        }
        
        bars = ax2.bar(info_data.keys(), info_data.values(), color='lightcoral', alpha=0.7)
        ax2.set_ylabel('数量')
        ax2.set_title('数据集基本信息统计')
        ax2.tick_params(axis='x', rotation=45)
        
        # 在柱状图上添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}', ha='center', va='bottom')
        
        plt.suptitle('图2: 数据集统计信息', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图2_数据集统计信息.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_figure3_eigenfaces(self):
        """图3: 特征脸可视化"""
        fig, axes = plt.subplots(3, 4, figsize=(12, 9))
        fig.suptitle('图3: 特征脸(Eigenfaces)可视化', fontsize=16, fontweight='bold')
        
        for i in range(12):
            row, col = i // 4, i % 4
            axes[row, col].imshow(self.eigenfaces[i], cmap='gray')
            axes[row, col].set_title(f'特征脸 {i+1}', fontsize=10, fontweight='bold')
            axes[row, col].axis('off')
            
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图3_特征脸可视化.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_figure4_pca_analysis(self):
        """图4: PCA降维效果分析"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 左图：解释方差比例
        explained_variance_ratio = self.pca.explained_variance_ratio_
        cumulative_variance = np.cumsum(explained_variance_ratio)
        
        ax1.plot(range(1, len(explained_variance_ratio) + 1), 
                cumulative_variance, 'b-', linewidth=2, marker='o', markersize=3)
        ax1.set_xlabel('主成分数量')
        ax1.set_ylabel('累积解释方差比例')
        ax1.set_title('PCA累积解释方差比例')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0.95, color='r', linestyle='--', alpha=0.7, label='95%阈值')
        ax1.legend()
        
        # 右图：降维前后特征维度对比
        dimensions = ['原始特征', 'PCA特征']
        dim_values = [self.X.shape[1], self.X_train_pca.shape[1]]
        
        bars = ax2.bar(dimensions, dim_values, color=['orange', 'green'], alpha=0.7)
        ax2.set_ylabel('特征维度')
        ax2.set_title('降维前后特征维度对比')
        
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{int(height)}', ha='center', va='bottom', fontweight='bold')
        
        plt.suptitle('图4: PCA降维效果分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图4_PCA降维效果分析.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def train_models_and_evaluate(self):
        """训练模型并评估性能"""
        print("训练多种分类器...")
        
        self.models = {
            'SVM': SVC(kernel='rbf', C=1000, gamma=0.001, random_state=42),
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'AdaBoost': AdaBoostClassifier(n_estimators=100, random_state=42),
            'Gradient Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'KNN': KNeighborsClassifier(n_neighbors=5)
        }
        
        self.results = {}
        training_times = {}
        
        for name, model in self.models.items():
            print(f"训练 {name}...")
            start_time = time()
            model.fit(self.X_train_pca, self.y_train)
            training_time = time() - start_time
            training_times[name] = training_time
            
            # 预测和评估
            y_pred = model.predict(self.X_test_pca)
            accuracy = accuracy_score(self.y_test, y_pred)
            
            self.results[name] = {
                'model': model,
                'y_pred': y_pred,
                'accuracy': accuracy,
                'training_time': training_time
            }
            
        return training_times
        
    def generate_figure5_model_performance(self):
        """图5: 模型性能对比"""
        training_times = self.train_models_and_evaluate()
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        algorithms = list(self.results.keys())
        accuracies = [self.results[alg]['accuracy'] for alg in algorithms]
        times = [training_times[alg] for alg in algorithms]
        
        # 准确率对比
        bars1 = ax1.bar(algorithms, accuracies, color='skyblue', alpha=0.8)
        ax1.set_ylabel('准确率')
        ax1.set_title('(a) 各算法准确率对比')
        ax1.tick_params(axis='x', rotation=45)
        ax1.set_ylim(0, 1)
        
        for bar, acc in zip(bars1, accuracies):
            ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                    f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 训练时间对比
        bars2 = ax2.bar(algorithms, times, color='lightcoral', alpha=0.8)
        ax2.set_ylabel('训练时间 (秒)')
        ax2.set_title('(b) 各算法训练时间对比')
        ax2.tick_params(axis='x', rotation=45)
        
        for bar, time_val in zip(bars2, times):
            ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.1,
                    f'{time_val:.2f}s', ha='center', va='bottom', fontweight='bold')
        
        # 性能-效率散点图
        ax3.scatter(times, accuracies, s=100, alpha=0.7, c=range(len(algorithms)), cmap='viridis')
        for i, alg in enumerate(algorithms):
            ax3.annotate(alg, (times[i], accuracies[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=9)
        ax3.set_xlabel('训练时间 (秒)')
        ax3.set_ylabel('准确率')
        ax3.set_title('(c) 性能-效率关系图')
        ax3.grid(True, alpha=0.3)
        
        # 算法排名
        performance_df = pd.DataFrame({
            'Algorithm': algorithms,
            'Accuracy': accuracies,
            'Training_Time': times
        })
        performance_df['Rank'] = performance_df['Accuracy'].rank(ascending=False)
        
        sorted_df = performance_df.sort_values('Rank')
        bars4 = ax4.barh(sorted_df['Algorithm'], sorted_df['Accuracy'], color='lightgreen', alpha=0.8)
        ax4.set_xlabel('准确率')
        ax4.set_title('(d) 算法性能排名')
        
        for i, (bar, acc) in enumerate(zip(bars4, sorted_df['Accuracy'])):
            ax4.text(bar.get_width() + 0.005, bar.get_y() + bar.get_height()/2.,
                    f'{acc:.3f}', ha='left', va='center', fontweight='bold')
        
        plt.suptitle('图5: 模型性能对比分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图5_模型性能对比.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_figure6_confusion_matrix(self):
        """图6: 最佳模型混淆矩阵"""
        # 找到准确率最高的模型
        best_model_name = max(self.results.keys(), 
                             key=lambda x: self.results[x]['accuracy'])
        best_result = self.results[best_model_name]
        
        # 计算混淆矩阵
        cm = confusion_matrix(self.y_test, best_result['y_pred'])
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.target_names,
                   yticklabels=self.target_names,
                   cbar_kws={'label': '样本数量'})
        
        plt.title(f'图6: {best_model_name}模型混淆矩阵\n(准确率: {best_result["accuracy"]:.3f})', 
                 fontsize=16, fontweight='bold')
        plt.xlabel('预测标签', fontsize=12)
        plt.ylabel('真实标签', fontsize=12)
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        
        plt.tight_layout()
        plt.savefig(f'{self.output_dir}/图6_最佳模型混淆矩阵.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_all_figures(self):
        """生成所有论文图表"""
        print("开始生成论文图表...")
        
        self.load_and_prepare_data()
        
        print("生成图1: 数据集样本展示...")
        self.generate_figure1_dataset_samples()
        
        print("生成图2: 数据集统计信息...")
        self.generate_figure2_dataset_statistics()
        
        print("生成图3: 特征脸可视化...")
        self.generate_figure3_eigenfaces()
        
        print("生成图4: PCA降维效果分析...")
        self.generate_figure4_pca_analysis()
        
        print("生成图5: 模型性能对比...")
        self.generate_figure5_model_performance()
        
        print("生成图6: 混淆矩阵...")
        self.generate_figure6_confusion_matrix()
        
        print(f"\n所有图表已生成完成！保存在 '{self.output_dir}' 文件夹中。")
        
        # 生成图表说明文档
        self.generate_figure_descriptions()
        
    def generate_figure_descriptions(self):
        """生成图表说明文档"""
        descriptions = """
# 论文图表说明文档

## 图1: LFW数据集样本展示
- 展示LFW数据集中不同人物的代表性人脸图像
- 用于说明数据集的多样性和图像质量
- 插入位置：第二章 2.1 数据分析

## 图2: 数据集统计信息
- 左图：各人物样本数量分布，展示数据集的类别不平衡情况
- 右图：数据集基本信息统计，包括总样本数、特征维度等
- 插入位置：第二章 2.1 数据分析

## 图3: 特征脸可视化
- 展示PCA提取的前12个特征脸
- 说明PCA如何提取人脸的主要特征
- 插入位置：第二章 2.3 特征提取

## 图4: PCA降维效果分析
- 左图：PCA累积解释方差比例，说明降维效果
- 右图：降维前后特征维度对比
- 插入位置：第二章 2.3 特征提取

## 图5: 模型性能对比分析
- (a) 各算法准确率对比
- (b) 各算法训练时间对比
- (c) 性能-效率关系图
- (d) 算法性能排名
- 插入位置：第四章 4.1 模型训练结果

## 图6: 最佳模型混淆矩阵
- 展示性能最佳模型的详细分类结果
- 分析各类别的识别准确率和误分类情况
- 插入位置：第四章 4.2 关键指标分析

## 使用说明
1. 所有图表已保存为高分辨率PNG格式
2. 可直接插入到论文相应位置
3. 图表标题和说明已包含在图片中
4. 建议在论文中添加详细的图表分析说明
"""
        
        with open(f'{self.output_dir}/图表说明文档.md', 'w', encoding='utf-8') as f:
            f.write(descriptions)

if __name__ == "__main__":
    generator = PaperFigureGenerator()
    generator.generate_all_figures()
