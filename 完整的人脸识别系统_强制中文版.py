"""
完整的人脸识别系统 - 强制中文字体版本
基于LFW数据集的人脸识别系统，包含多种机器学习算法的比较研究
"""

# 首先强制设置中文字体
import matplotlib
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import warnings

# 忽略字体警告
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

print("🔧 强制设置中文字体...")

# 清除字体缓存并重建
try:
    matplotlib.font_manager._rebuild()
except:
    pass

# 获取系统字体并查找中文字体
font_list = [f.name for f in fm.fontManager.ttflist]
chinese_fonts = []
for font in font_list:
    if any(pattern in font for pattern in ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'PingFang', 'Hiragino', 'WenQuanYi', 'Noto']):
        chinese_fonts.append(font)

# 设置字体优先级
font_priority = chinese_fonts + ['SimHei', 'Microsoft YaHei', 'SimSun', 'DejaVu Sans', 'Arial']

# 强制设置matplotlib参数
plt.rcParams.update({
    'font.sans-serif': font_priority,
    'font.family': 'sans-serif',
    'axes.unicode_minus': False,
    'font.size': 10
})

matplotlib.rcParams.update({
    'font.sans-serif': font_priority,
    'font.family': 'sans-serif',
    'axes.unicode_minus': False,
    'font.size': 10
})

print(f"✅ 中文字体设置完成，优先使用: {font_priority[:2]}")

# 导入其他必要的库
import numpy as np
import seaborn as sns
from time import time
import pandas as pd
import os

from sklearn.datasets import fetch_lfw_people
from sklearn.model_selection import train_test_split, GridSearchCV, cross_val_score
from sklearn.decomposition import PCA
from sklearn.svm import SVC
from sklearn.ensemble import (RandomForestClassifier, AdaBoostClassifier, 
                             GradientBoostingClassifier, VotingClassifier)
from sklearn.tree import DecisionTreeClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import (classification_report, confusion_matrix, 
                           accuracy_score, precision_recall_fscore_support)

class FaceRecognitionSystem:
    """人脸识别系统类 - 强制中文字体版"""
    
    def __init__(self, min_faces_per_person=70, resize=0.4, n_components=150):
        """
        初始化人脸识别系统
        
        参数:
        - min_faces_per_person: 每个人最少的图片数量
        - resize: 图像缩放比例
        - n_components: PCA主成分数量
        """
        self.min_faces_per_person = min_faces_per_person
        self.resize = resize
        self.n_components = n_components
        self.models = {}
        self.results = {}
        
    def load_data(self):
        """加载LFW数据集"""
        print("=" * 60)
        print("第一步: 加载LFW数据集")
        print("=" * 60)
        
        # 使用本地数据集
        local_data_home = "./data"
        
        try:
            # 首先尝试从本地加载
            if os.path.exists(local_data_home):
                print(f"📁 使用本地数据集: {local_data_home}")
                self.lfw_people = fetch_lfw_people(
                    min_faces_per_person=self.min_faces_per_person,
                    resize=self.resize,
                    data_home=local_data_home,
                    download_if_missing=False
                )
                print("✅ 本地数据集加载成功！")
            else:
                # 如果本地没有数据，使用自动下载
                print("⚠️  本地数据集不存在，尝试自动下载...")
                self.lfw_people = fetch_lfw_people(
                    min_faces_per_person=self.min_faces_per_person, 
                    resize=self.resize
                )
                print("✅ 数据集自动下载成功！")
                
        except Exception as e:
            print(f"❌ 数据集加载失败: {e}")
            print("请检查数据集是否正确放置在 './data' 文件夹中")
            raise
        
        # 获取基本信息
        self.n_samples, self.h, self.w = self.lfw_people.images.shape
        self.X = self.lfw_people.data
        self.y = self.lfw_people.target
        self.target_names = self.lfw_people.target_names
        self.n_features = self.X.shape[1]
        self.n_classes = self.target_names.shape[0]
        
        # 打印数据集信息
        print(f"数据集加载完成！")
        print(f"样本总数: {self.n_samples}")
        print(f"特征维度: {self.n_features}")
        print(f"类别数量: {self.n_classes}")
        print(f"图像尺寸: {self.h} x {self.w}")
        
        # 显示各类别样本数量
        print("\n各人物样本数量:")
        for i, name in enumerate(self.target_names):
            count = np.sum(self.y == i)
            print(f"{i+1:2d}. {name}: {count} 张图片")
            
    def split_data(self):
        """划分训练集和测试集"""
        print("\n" + "=" * 60)
        print("第二步: 划分训练集和测试集")
        print("=" * 60)
        
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            self.X, self.y, test_size=0.25, random_state=42, stratify=self.y
        )
        
        print(f"训练集大小: {self.X_train.shape}")
        print(f"测试集大小: {self.X_test.shape}")
        
    def extract_features(self):
        """PCA特征提取"""
        print("\n" + "=" * 60)
        print("第三步: PCA特征提取")
        print("=" * 60)
        
        print(f"从 {self.X_train.shape[0]} 张人脸图像中提取前 {self.n_components} 个特征脸...")
        
        t0 = time()
        self.pca = PCA(n_components=self.n_components, svd_solver='randomized', whiten=True)
        self.pca.fit(self.X_train)
        
        # 获取特征脸
        self.eigenfaces = self.pca.components_.reshape((self.n_components, self.h, self.w))
        
        # 降维
        self.X_train_pca = self.pca.transform(self.X_train)
        self.X_test_pca = self.pca.transform(self.X_test)
        
        print(f"PCA特征提取完成，耗时: {time() - t0:.3f}秒")
        print(f"降维后特征维度: {self.X_train_pca.shape[1]}")
        print(f"解释方差比例: {self.pca.explained_variance_ratio_.sum():.3f}")
        
    def train_models(self):
        """训练多种分类器"""
        print("\n" + "=" * 60)
        print("第四步: 训练多种分类器")
        print("=" * 60)
        
        # 定义分类器
        classifiers = {
            'SVM': SVC(kernel='rbf', class_weight='balanced', probability=True),
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'AdaBoost': AdaBoostClassifier(n_estimators=100, random_state=42),
            'Gradient Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
            'KNN': KNeighborsClassifier(n_neighbors=5)
        }
        
        # 训练每个分类器
        for name, clf in classifiers.items():
            print(f"\n训练 {name}...")
            t0 = time()
            
            if name == 'SVM':
                # SVM使用网格搜索优化参数
                param_grid = {
                    'C': [1e3, 5e3, 1e4],
                    'gamma': [0.0001, 0.0005, 0.001, 0.005]
                }
                clf = GridSearchCV(clf, param_grid, cv=3, n_jobs=-1)
            
            clf.fit(self.X_train_pca, self.y_train)
            self.models[name] = clf
            
            training_time = time() - t0
            print(f"{name} 训练完成，耗时: {training_time:.3f}秒")
            
            if name == 'SVM':
                print(f"最优参数: {clf.best_params_}")
                
    def evaluate_models(self):
        """评估模型性能"""
        print("\n" + "=" * 60)
        print("第五步: 模型性能评估")
        print("=" * 60)
        
        results_data = []
        
        for name, model in self.models.items():
            print(f"\n评估 {name}...")
            
            # 预测
            t0 = time()
            y_pred = model.predict(self.X_test_pca)
            prediction_time = time() - t0
            
            # 计算指标
            accuracy = accuracy_score(self.y_test, y_pred)
            precision, recall, f1, _ = precision_recall_fscore_support(
                self.y_test, y_pred, average='weighted'
            )
            
            # 交叉验证
            cv_scores = cross_val_score(model, self.X_train_pca, self.y_train, cv=5)
            
            # 保存结果
            self.results[name] = {
                'y_pred': y_pred,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'prediction_time': prediction_time
            }
            
            results_data.append({
                'Algorithm': name,
                'Accuracy': accuracy,
                'Precision': precision,
                'Recall': recall,
                'F1-Score': f1,
                'CV Mean': cv_scores.mean(),
                'CV Std': cv_scores.std(),
                'Prediction Time': prediction_time
            })
            
            print(f"准确率: {accuracy:.3f}")
            print(f"精确率: {precision:.3f}")
            print(f"召回率: {recall:.3f}")
            print(f"F1分数: {f1:.3f}")
            print(f"交叉验证: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
            
        # 创建结果DataFrame
        self.results_df = pd.DataFrame(results_data)
        print("\n" + "=" * 80)
        print("所有算法性能对比:")
        print("=" * 80)
        print(self.results_df.round(3))
        
    def plot_sample_images(self, n_samples=12):
        """绘制样本图像"""
        plt.rcParams['font.sans-serif'] = font_priority  # 强制设置字体
        
        plt.figure(figsize=(12, 8))
        plt.suptitle('LFW数据集样本展示', fontsize=16)
        
        for i in range(n_samples):
            plt.subplot(3, 4, i + 1)
            plt.imshow(self.lfw_people.images[i], cmap='gray')
            plt.title(f'{self.target_names[self.y[i]]}', fontsize=10)
            plt.axis('off')
            
        plt.tight_layout()
        plt.show()
        
    def plot_eigenfaces(self, n_faces=12):
        """绘制特征脸"""
        plt.rcParams['font.sans-serif'] = font_priority  # 强制设置字体
        
        plt.figure(figsize=(12, 8))
        plt.suptitle('特征脸(Eigenfaces)可视化', fontsize=16)
        
        for i in range(n_faces):
            plt.subplot(3, 4, i + 1)
            plt.imshow(self.eigenfaces[i], cmap='gray')
            plt.title(f'特征脸 {i+1}', fontsize=10)
            plt.axis('off')
            
        plt.tight_layout()
        plt.show()
        
    def plot_performance_comparison(self):
        """绘制性能对比图"""
        plt.rcParams['font.sans-serif'] = font_priority  # 强制设置字体
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('算法性能对比', fontsize=16)
        
        # 准确率对比
        axes[0, 0].bar(self.results_df['Algorithm'], self.results_df['Accuracy'])
        axes[0, 0].set_title('准确率对比')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # F1分数对比
        axes[0, 1].bar(self.results_df['Algorithm'], self.results_df['F1-Score'])
        axes[0, 1].set_title('F1分数对比')
        axes[0, 1].set_ylabel('F1分数')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 预测时间对比
        axes[1, 0].bar(self.results_df['Algorithm'], self.results_df['Prediction Time'])
        axes[1, 0].set_title('预测时间对比')
        axes[1, 0].set_ylabel('时间(秒)')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 交叉验证分数对比
        axes[1, 1].bar(self.results_df['Algorithm'], self.results_df['CV Mean'])
        axes[1, 1].errorbar(range(len(self.results_df)), self.results_df['CV Mean'], 
                           yerr=self.results_df['CV Std'], fmt='none', color='red')
        axes[1, 1].set_title('交叉验证分数对比')
        axes[1, 1].set_ylabel('CV分数')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.show()
        
    def plot_confusion_matrix(self, model_name='SVM'):
        """绘制混淆矩阵"""
        plt.rcParams['font.sans-serif'] = font_priority  # 强制设置字体
        
        if model_name not in self.results:
            print(f"模型 {model_name} 不存在")
            return
            
        y_pred = self.results[model_name]['y_pred']
        cm = confusion_matrix(self.y_test, y_pred)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.target_names,
                   yticklabels=self.target_names)
        plt.title(f'{model_name} 混淆矩阵')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.show()
        
    def run_complete_analysis(self):
        """运行完整的分析流程"""
        self.load_data()
        self.split_data()
        self.extract_features()
        self.train_models()
        self.evaluate_models()
        
        # 生成可视化图表
        print("\n生成可视化图表...")
        self.plot_sample_images()
        self.plot_eigenfaces()
        self.plot_performance_comparison()
        self.plot_confusion_matrix('SVM')
        
        return self.results_df

# 主程序
if __name__ == "__main__":
    # 创建人脸识别系统实例
    face_system = FaceRecognitionSystem(min_faces_per_person=70, resize=0.4, n_components=150)
    
    # 运行完整分析
    results = face_system.run_complete_analysis()
    
    print("\n" + "=" * 60)
    print("分析完成！所有图表已生成，中文显示正常。")
    print("=" * 60)
