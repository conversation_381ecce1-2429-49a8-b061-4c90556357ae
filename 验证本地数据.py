"""
验证本地LFW数据集
检查本地数据集是否正确配置并可以正常加载
"""

import os
from sklearn.datasets import fetch_lfw_people
import numpy as np
import matplotlib.pyplot as plt

def check_local_data_structure():
    """检查本地数据结构"""
    print("🔍 检查本地数据结构...")
    
    expected_paths = [
        "./data",
        "./data/lfw_home", 
        "./data/lfw_home/lfw_funneled"
    ]
    
    all_exist = True
    for path in expected_paths:
        if os.path.exists(path):
            print(f"✅ {path} - 存在")
        else:
            print(f"❌ {path} - 不存在")
            all_exist = False
    
    if all_exist:
        # 检查是否有图像文件
        lfw_funneled_path = "./data/lfw_home/lfw_funneled"
        if os.path.exists(lfw_funneled_path):
            subdirs = [d for d in os.listdir(lfw_funneled_path) 
                      if os.path.isdir(os.path.join(lfw_funneled_path, d))]
            print(f"📁 找到 {len(subdirs)} 个人物文件夹")
            
            if len(subdirs) > 0:
                print("前几个人物文件夹:")
                for i, subdir in enumerate(subdirs[:5]):
                    person_path = os.path.join(lfw_funneled_path, subdir)
                    image_files = [f for f in os.listdir(person_path) 
                                 if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    print(f"  {i+1}. {subdir}: {len(image_files)} 张图片")
            
            return True
        else:
            print("❌ lfw_funneled 文件夹不存在")
            return False
    else:
        return False

def test_local_data_loading():
    """测试本地数据加载"""
    print("\n🚀 测试本地数据加载...")
    
    local_data_home = "./data"
    
    try:
        # 尝试加载本地数据
        lfw_people = fetch_lfw_people(
            min_faces_per_person=70,
            resize=0.4,
            data_home=local_data_home
        )
        
        print("✅ 本地数据集加载成功！")
        
        # 显示基本信息
        n_samples, h, w = lfw_people.images.shape
        X = lfw_people.data
        y = lfw_people.target
        target_names = lfw_people.target_names
        
        print(f"\n📊 数据集信息:")
        print(f"样本总数: {n_samples}")
        print(f"图像尺寸: {h} x {w}")
        print(f"特征维度: {X.shape[1]}")
        print(f"类别数量: {len(target_names)}")
        
        print(f"\n👥 包含的人物:")
        for i, name in enumerate(target_names):
            count = np.sum(y == i)
            print(f"{i+1:2d}. {name}: {count} 张图片")
        
        return True, lfw_people
        
    except Exception as e:
        print(f"❌ 本地数据加载失败: {e}")
        return False, None

def show_sample_images(lfw_people):
    """显示样本图像"""
    print("\n🖼️  显示样本图像...")
    
    try:
        fig, axes = plt.subplots(2, 3, figsize=(12, 8))
        fig.suptitle('本地LFW数据集样本验证', fontsize=16)
        
        # 选择不同人物的图像
        selected_indices = []
        used_people = set()
        
        for i in range(len(lfw_people.target)):
            if lfw_people.target[i] not in used_people and len(selected_indices) < 6:
                selected_indices.append(i)
                used_people.add(lfw_people.target[i])
        
        for idx, img_idx in enumerate(selected_indices):
            row, col = idx // 3, idx % 3
            axes[row, col].imshow(lfw_people.images[img_idx], cmap='gray')
            axes[row, col].set_title(f'{lfw_people.target_names[lfw_people.target[img_idx]]}')
            axes[row, col].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        print("✅ 样本图像显示成功！")
        
    except Exception as e:
        print(f"⚠️  图像显示失败: {e}")

def compare_with_auto_download():
    """比较本地数据和自动下载数据"""
    print("\n🔄 比较本地数据和自动下载数据...")
    
    try:
        # 加载本地数据
        local_data = fetch_lfw_people(
            min_faces_per_person=70,
            resize=0.4,
            data_home="./data"
        )
        
        # 尝试自动下载数据（如果可能）
        try:
            auto_data = fetch_lfw_people(
                min_faces_per_person=70,
                resize=0.4
            )
            
            print("📊 数据对比:")
            print(f"本地数据样本数: {local_data.images.shape[0]}")
            print(f"自动下载样本数: {auto_data.images.shape[0]}")
            print(f"本地数据类别数: {len(local_data.target_names)}")
            print(f"自动下载类别数: {len(auto_data.target_names)}")
            
            if local_data.images.shape == auto_data.images.shape:
                print("✅ 数据维度一致")
            else:
                print("⚠️  数据维度不一致")
                
        except Exception as e:
            print(f"⚠️  无法加载自动下载数据进行比较: {e}")
            
    except Exception as e:
        print(f"❌ 比较失败: {e}")

def generate_data_report():
    """生成数据报告"""
    print("\n📋 生成数据验证报告...")
    
    report = []
    report.append("# LFW本地数据集验证报告\n")
    
    # 检查数据结构
    structure_ok = check_local_data_structure()
    report.append(f"## 数据结构检查: {'✅ 通过' if structure_ok else '❌ 失败'}\n")
    
    # 测试数据加载
    loading_ok, lfw_data = test_local_data_loading()
    report.append(f"## 数据加载测试: {'✅ 通过' if loading_ok else '❌ 失败'}\n")
    
    if loading_ok and lfw_data:
        n_samples, h, w = lfw_data.images.shape
        report.append(f"## 数据集信息\n")
        report.append(f"- 样本总数: {n_samples}\n")
        report.append(f"- 图像尺寸: {h} x {w}\n")
        report.append(f"- 特征维度: {lfw_data.data.shape[1]}\n")
        report.append(f"- 类别数量: {len(lfw_data.target_names)}\n")
        
        report.append(f"\n## 人物列表\n")
        for i, name in enumerate(lfw_data.target_names):
            count = np.sum(lfw_data.target == i)
            report.append(f"{i+1}. {name}: {count} 张图片\n")
    
    # 保存报告
    with open("本地数据验证报告.md", "w", encoding="utf-8") as f:
        f.writelines(report)
    
    print("📄 验证报告已保存到: 本地数据验证报告.md")
    
    return structure_ok and loading_ok

def main():
    """主函数"""
    print("=" * 60)
    print("LFW本地数据集验证工具")
    print("=" * 60)
    
    # 检查数据结构
    structure_ok = check_local_data_structure()
    
    if not structure_ok:
        print("\n❌ 数据结构检查失败！")
        print("请确保数据集正确放置在以下位置:")
        print("./data/lfw_home/lfw_funneled/")
        return False
    
    # 测试数据加载
    loading_ok, lfw_data = test_local_data_loading()
    
    if not loading_ok:
        print("\n❌ 数据加载失败！")
        return False
    
    # 显示样本图像
    show_sample_images(lfw_data)
    
    # 比较数据
    compare_with_auto_download()
    
    # 生成报告
    generate_data_report()
    
    print("\n" + "=" * 60)
    print("🎉 本地数据集验证完成！")
    print("=" * 60)
    print("现在您可以运行主程序:")
    print("1. python 论文图表生成器.py")
    print("2. python 完整的人脸识别系统.py")
    print("3. python Lfw_face_recognition_svm_ensemble-master/face_recognition.py")
    
    return True

if __name__ == "__main__":
    main()
