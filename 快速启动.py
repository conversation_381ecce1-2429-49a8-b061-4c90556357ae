"""
快速启动脚本
一键运行人脸识别系统的所有功能
"""

import os
import sys

def print_banner():
    """打印欢迎横幅"""
    print("=" * 70)
    print("🎓 基于LFW数据集的人脸识别系统")
    print("📚 非结构化数据挖掘期末作业")
    print("=" * 70)

def check_data():
    """检查数据集是否存在"""
    data_path = "./data/lfw_home/lfw_funneled"
    if os.path.exists(data_path):
        print("✅ 本地数据集检查通过")
        return True
    else:
        print("❌ 本地数据集不存在")
        print("请确保数据集放置在 './data/lfw_home/lfw_funneled' 目录下")
        return False

def show_menu():
    """显示菜单"""
    print("\n📋 请选择要执行的功能:")
    print("1. 🧪 验证本地数据集")
    print("2. 📊 生成论文图表（强制中文版）")
    print("3. 🤖 运行完整人脸识别系统（强制中文版）")
    print("4. 📈 生成论文图表（原版）")
    print("5. 🔬 运行完整系统（原版）")
    print("6. 🔧 强制中文字体设置测试")
    print("7. 📖 查看项目说明")
    print("0. 🚪 退出")
    print("-" * 50)

def run_script(script_name):
    """运行指定脚本"""
    if not os.path.exists(script_name):
        print(f"❌ 脚本文件不存在: {script_name}")
        return False
    
    print(f"🚀 正在运行: {script_name}")
    print("-" * 50)
    
    try:
        # 使用exec运行脚本
        with open(script_name, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        exec(script_content)
        print(f"✅ {script_name} 运行完成")
        return True
        
    except Exception as e:
        print(f"❌ 运行 {script_name} 时出错: {e}")
        return False

def show_project_info():
    """显示项目信息"""
    info = """
📖 项目信息
=" * 50
🎯 项目名称: 基于LFW数据集的人脸识别系统
📚 课程: 非结构化数据挖掘
🔬 技术栈: Python + scikit-learn + matplotlib

📁 主要文件:
- 论文图表生成器_强制中文版.py: 生成所有论文图表（推荐）
- 完整的人脸识别系统_强制中文版.py: 完整系统分析（推荐）
- 基于LFW数据集的人脸识别系统论文.md: 完整论文
- 项目运行指南.md: 详细使用说明

🎨 生成的图表:
1. LFW数据集样本展示
2. 数据集统计信息
3. 特征脸可视化
4. PCA降维效果分析
5. 模型性能对比分析
6. 最佳模型混淆矩阵

🤖 实现的算法:
- SVM (支持向量机)
- Random Forest (随机森林)
- AdaBoost (自适应提升)
- Gradient Boosting (梯度提升)
- KNN (K近邻)

✨ 特色功能:
- 强制中文字体设置，确保图表中文正常显示
- 本地数据集支持，无需网络下载
- 完整的性能评估和可视化
- 详细的中文注释和文档

📊 预期结果:
- 生成6个高质量的论文图表
- 完整的算法性能对比分析
- 可直接用于期末作业提交的论文
"""
    print(info)

def main():
    """主函数"""
    print_banner()
    
    # 检查数据集
    if not check_data():
        print("\n💡 建议:")
        print("1. 确保已下载LFW数据集")
        print("2. 将数据集解压到 './data/lfw_home/lfw_funneled' 目录")
        print("3. 或运行验证脚本检查数据集配置")
        print("\n按任意键继续...")
        input()
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选项 (0-7): ").strip()
            
            if choice == '0':
                print("👋 感谢使用！祝您期末作业取得好成绩！")
                break
                
            elif choice == '1':
                print("🧪 验证本地数据集...")
                run_script("验证本地数据.py")
                
            elif choice == '2':
                print("📊 生成论文图表（强制中文版）...")
                run_script("论文图表生成器_强制中文版.py")
                
            elif choice == '3':
                print("🤖 运行完整人脸识别系统（强制中文版）...")
                run_script("完整的人脸识别系统_强制中文版.py")
                
            elif choice == '4':
                print("📈 生成论文图表（原版）...")
                run_script("论文图表生成器.py")
                
            elif choice == '5':
                print("🔬 运行完整系统（原版）...")
                run_script("完整的人脸识别系统.py")
                
            elif choice == '6':
                print("🔧 强制中文字体设置测试...")
                run_script("强制中文字体设置.py")
                
            elif choice == '7':
                show_project_info()
                
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            
        print("\n" + "=" * 50)
        input("按回车键继续...")

if __name__ == "__main__":
    main()
