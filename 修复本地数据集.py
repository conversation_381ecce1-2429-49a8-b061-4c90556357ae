"""
修复本地LFW数据集配置
解决sklearn无法正确加载本地数据的问题
"""

import os
import numpy as np
from sklearn.datasets import fetch_lfw_people
from sklearn.datasets._lfw import _fetch_lfw_people
import warnings

def create_missing_metadata_files():
    """创建缺失的元数据文件"""
    print("🔧 创建缺失的元数据文件...")
    
    lfw_home = "./data/lfw_home"
    
    # 需要创建的文件列表
    metadata_files = [
        "pairsDevTrain.txt",
        "pairsDevTest.txt", 
        "pairs.txt"
    ]
    
    # 创建简单的元数据文件内容
    for filename in metadata_files:
        filepath = os.path.join(lfw_home, filename)
        if not os.path.exists(filepath):
            print(f"创建文件: {filename}")
            with open(filepath, 'w') as f:
                f.write("# LFW metadata file\n")
                f.write("# Created for local dataset\n")
        else:
            print(f"文件已存在: {filename}")

def load_lfw_data_directly():
    """直接加载LFW数据，绕过网络下载"""
    print("🚀 直接加载本地LFW数据...")
    
    lfw_funneled_path = "./data/lfw_home/lfw_funneled"
    
    if not os.path.exists(lfw_funneled_path):
        print("❌ LFW数据文件夹不存在")
        return None
    
    # 获取所有人物文件夹
    person_dirs = [d for d in os.listdir(lfw_funneled_path) 
                   if os.path.isdir(os.path.join(lfw_funneled_path, d))]
    
    print(f"📁 找到 {len(person_dirs)} 个人物文件夹")
    
    # 统计每个人的图片数量
    person_counts = {}
    for person_dir in person_dirs:
        person_path = os.path.join(lfw_funneled_path, person_dir)
        image_files = [f for f in os.listdir(person_path) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        person_counts[person_dir] = len(image_files)
    
    # 过滤出图片数量>=70的人物
    min_faces = 70
    valid_persons = {name: count for name, count in person_counts.items() 
                    if count >= min_faces}
    
    print(f"🎯 找到 {len(valid_persons)} 个人物（每人至少{min_faces}张图片）:")
    for name, count in valid_persons.items():
        print(f"  - {name}: {count} 张图片")
    
    return valid_persons

def test_sklearn_loading_with_offline_mode():
    """测试sklearn在离线模式下的加载"""
    print("\n🔄 测试sklearn离线加载...")
    
    # 设置环境变量禁用网络下载
    os.environ['SKLEARN_SKIP_NETWORK_TESTS'] = '1'
    
    try:
        # 忽略网络相关警告
        with warnings.catch_warnings():
            warnings.filterwarnings("ignore", category=UserWarning)
            
            # 尝试加载数据
            lfw_people = fetch_lfw_people(
                min_faces_per_person=70,
                resize=0.4,
                data_home="./data",
                download_if_missing=False  # 禁止下载
            )
            
            print("✅ sklearn离线加载成功！")
            
            # 显示数据信息
            n_samples, h, w = lfw_people.images.shape
            print(f"样本数量: {n_samples}")
            print(f"图像尺寸: {h} x {w}")
            print(f"类别数量: {len(lfw_people.target_names)}")
            
            return True, lfw_people
            
    except Exception as e:
        print(f"❌ sklearn离线加载失败: {e}")
        return False, None

def create_custom_lfw_loader():
    """创建自定义的LFW数据加载器"""
    print("\n🛠️  创建自定义LFW数据加载器...")
    
    # 创建自定义加载器代码
    custom_loader_code = '''
"""
自定义LFW数据加载器
绕过sklearn的网络下载限制
"""

import os
import numpy as np
from PIL import Image
from sklearn.utils import Bunch

def load_local_lfw_people(data_home="./data", min_faces_per_person=70, resize=0.4):
    """
    从本地加载LFW数据集
    
    参数:
    - data_home: 数据根目录
    - min_faces_per_person: 每个人最少的图片数量
    - resize: 图像缩放比例
    
    返回:
    - Bunch对象，包含images, data, target, target_names等属性
    """
    
    lfw_funneled_path = os.path.join(data_home, "lfw_home", "lfw_funneled")
    
    if not os.path.exists(lfw_funneled_path):
        raise FileNotFoundError(f"LFW数据路径不存在: {lfw_funneled_path}")
    
    # 获取所有人物文件夹
    person_dirs = [d for d in os.listdir(lfw_funneled_path) 
                   if os.path.isdir(os.path.join(lfw_funneled_path, d))]
    
    # 统计每个人的图片数量并过滤
    valid_persons = []
    person_image_paths = {}
    
    for person_dir in person_dirs:
        person_path = os.path.join(lfw_funneled_path, person_dir)
        image_files = [f for f in os.listdir(person_path) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        
        if len(image_files) >= min_faces_per_person:
            valid_persons.append(person_dir)
            person_image_paths[person_dir] = [
                os.path.join(person_path, img) for img in image_files
            ]
    
    print(f"找到 {len(valid_persons)} 个有效人物")
    
    # 加载图像数据
    images = []
    targets = []
    target_names = sorted(valid_persons)
    
    for person_idx, person_name in enumerate(target_names):
        for img_path in person_image_paths[person_name]:
            try:
                # 加载并处理图像
                img = Image.open(img_path).convert('L')  # 转为灰度图
                
                # 调整大小
                if resize != 1.0:
                    new_size = (int(img.width * resize), int(img.height * resize))
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # 转为numpy数组
                img_array = np.array(img, dtype=np.float32) / 255.0
                
                images.append(img_array)
                targets.append(person_idx)
                
            except Exception as e:
                print(f"跳过损坏的图像: {img_path}, 错误: {e}")
                continue
    
    # 转换为numpy数组
    images = np.array(images)
    targets = np.array(targets)
    
    # 创建data数组（展平的图像）
    n_samples, h, w = images.shape
    data = images.reshape(n_samples, h * w)
    
    print(f"加载完成: {n_samples} 个样本, {len(target_names)} 个类别")
    
    return Bunch(
        images=images,
        data=data,
        target=targets,
        target_names=np.array(target_names),
        DESCR="Local LFW dataset"
    )

# 使用示例
if __name__ == "__main__":
    lfw_people = load_local_lfw_people()
    print(f"样本数量: {lfw_people.images.shape[0]}")
    print(f"图像尺寸: {lfw_people.images.shape[1:3]}")
    print(f"类别数量: {len(lfw_people.target_names)}")
'''
    
    # 保存自定义加载器
    with open("自定义LFW加载器.py", "w", encoding="utf-8") as f:
        f.write(custom_loader_code)
    
    print("✅ 自定义加载器已创建: 自定义LFW加载器.py")

def test_custom_loader():
    """测试自定义加载器"""
    print("\n🧪 测试自定义加载器...")
    
    try:
        # 导入自定义加载器
        exec(open("自定义LFW加载器.py").read())
        
        # 使用自定义加载器
        from 自定义LFW加载器 import load_local_lfw_people
        
        lfw_people = load_local_lfw_people(
            data_home="./data",
            min_faces_per_person=70,
            resize=0.4
        )
        
        print("✅ 自定义加载器测试成功！")
        
        # 显示信息
        n_samples, h, w = lfw_people.images.shape
        print(f"样本数量: {n_samples}")
        print(f"图像尺寸: {h} x {w}")
        print(f"类别数量: {len(lfw_people.target_names)}")
        print(f"人物列表: {lfw_people.target_names}")
        
        return True, lfw_people
        
    except Exception as e:
        print(f"❌ 自定义加载器测试失败: {e}")
        return False, None

def main():
    """主函数"""
    print("=" * 60)
    print("LFW本地数据集修复工具")
    print("=" * 60)
    
    # 1. 创建元数据文件
    create_missing_metadata_files()
    
    # 2. 直接加载数据统计
    valid_persons = load_lfw_data_directly()
    
    if not valid_persons:
        print("❌ 无法找到有效的LFW数据")
        return False
    
    # 3. 测试sklearn离线加载
    sklearn_success, sklearn_data = test_sklearn_loading_with_offline_mode()
    
    # 4. 创建自定义加载器
    create_custom_loader()
    
    # 5. 测试自定义加载器
    custom_success, custom_data = test_custom_loader()
    
    print("\n" + "=" * 60)
    print("修复结果总结:")
    print("=" * 60)
    print(f"sklearn离线加载: {'✅ 成功' if sklearn_success else '❌ 失败'}")
    print(f"自定义加载器: {'✅ 成功' if custom_success else '❌ 失败'}")
    
    if sklearn_success:
        print("\n🎉 sklearn可以正常使用本地数据！")
        print("现在可以运行主程序了。")
    elif custom_success:
        print("\n🔧 sklearn有问题，但自定义加载器可用！")
        print("建议使用自定义加载器版本的程序。")
    else:
        print("\n❌ 两种方法都失败了，请检查数据集。")
    
    return sklearn_success or custom_success

if __name__ == "__main__":
    main()
