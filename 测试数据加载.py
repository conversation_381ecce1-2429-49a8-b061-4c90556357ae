"""
测试LFW数据集加载
用于验证数据是否正确下载和加载
"""

from sklearn.datasets import fetch_lfw_people
import numpy as np
import matplotlib.pyplot as plt
import os
import sys

def test_lfw_data_loading():
    """测试LFW数据集加载"""
    print("=" * 60)
    print("LFW数据集加载测试")
    print("=" * 60)
    
    try:
        print("正在尝试加载LFW数据集...")
        print("注意：首次运行可能需要下载数据，请耐心等待...")
        
        # 尝试加载数据
        lfw_people = fetch_lfw_people(min_faces_per_person=70, resize=0.4)
        
        # 获取基本信息
        n_samples, h, w = lfw_people.images.shape
        X = lfw_people.data
        y = lfw_people.target
        target_names = lfw_people.target_names
        n_features = X.shape[1]
        n_classes = len(target_names)
        
        print("✅ 数据集加载成功！")
        print("\n数据集基本信息:")
        print(f"📊 样本总数: {n_samples}")
        print(f"🖼️  图像尺寸: {h} x {w}")
        print(f"🔢 特征维度: {n_features}")
        print(f"👥 类别数量: {n_classes}")
        print(f"📁 数据形状: {X.shape}")
        print(f"🏷️  标签形状: {y.shape}")
        
        print("\n包含的人物:")
        for i, name in enumerate(target_names):
            count = np.sum(y == i)
            print(f"{i+1:2d}. {name}: {count} 张图片")
        
        # 显示数据存储位置
        from sklearn.datasets._base import get_data_home
        data_home = get_data_home()
        print(f"\n💾 数据存储位置: {data_home}")
        
        # 测试数据质量
        print("\n🔍 数据质量检查:")
        print(f"像素值范围: [{X.min():.3f}, {X.max():.3f}]")
        print(f"是否包含NaN: {np.isnan(X).any()}")
        print(f"是否包含无穷值: {np.isinf(X).any()}")
        
        # 显示样本图像
        show_sample_images(lfw_people, n_samples=6)
        
        return True, lfw_people
        
    except Exception as e:
        print(f"❌ 数据集加载失败!")
        print(f"错误信息: {str(e)}")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN")
        print("3. 手动下载数据集")
        print("4. 查看'数据下载指南.md'获取详细说明")
        
        return False, None

def show_sample_images(lfw_people, n_samples=6):
    """显示样本图像"""
    try:
        print(f"\n🖼️  显示 {n_samples} 个样本图像...")
        
        fig, axes = plt.subplots(2, 3, figsize=(12, 8))
        fig.suptitle('LFW数据集样本展示', fontsize=16)
        
        # 选择不同人物的图像
        selected_indices = []
        used_people = set()
        
        for i in range(len(lfw_people.target)):
            if lfw_people.target[i] not in used_people and len(selected_indices) < n_samples:
                selected_indices.append(i)
                used_people.add(lfw_people.target[i])
        
        for idx, img_idx in enumerate(selected_indices):
            row, col = idx // 3, idx % 3
            axes[row, col].imshow(lfw_people.images[img_idx], cmap='gray')
            axes[row, col].set_title(f'{lfw_people.target_names[lfw_people.target[img_idx]]}')
            axes[row, col].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        print("✅ 样本图像显示成功!")
        
    except Exception as e:
        print(f"⚠️  图像显示失败: {e}")

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'numpy', 'matplotlib', 'sklearn', 'pandas'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n请安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_disk_space():
    """检查磁盘空间"""
    try:
        import shutil
        from sklearn.datasets._base import get_data_home
        
        data_home = get_data_home()
        free_space = shutil.disk_usage(data_home).free / (1024**3)  # GB
        
        print(f"💾 可用磁盘空间: {free_space:.2f} GB")
        
        if free_space < 1:
            print("⚠️  磁盘空间可能不足，建议至少保留1GB空间")
            return False
        
        return True
        
    except Exception as e:
        print(f"⚠️  无法检查磁盘空间: {e}")
        return True

def main():
    """主函数"""
    print("🚀 开始LFW数据集测试...")
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请先安装必要的包")
        return
    
    # 检查磁盘空间
    check_disk_space()
    
    # 测试数据加载
    success, lfw_data = test_lfw_data_loading()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 测试完成！数据集准备就绪！")
        print("=" * 60)
        print("现在您可以运行主程序:")
        print("1. python 论文图表生成器.py")
        print("2. python 完整的人脸识别系统.py")
    else:
        print("\n" + "=" * 60)
        print("❌ 测试失败！请查看错误信息并解决问题")
        print("=" * 60)
        print("建议查看'数据下载指南.md'获取详细帮助")

if __name__ == "__main__":
    main()
