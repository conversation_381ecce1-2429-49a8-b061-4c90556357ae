
"""
自定义LFW数据加载器
绕过sklearn的网络下载限制
"""

import os
import numpy as np
from PIL import Image
from sklearn.utils import Bunch

def load_local_lfw_people(data_home="./data", min_faces_per_person=70, resize=0.4):
    """
    从本地加载LFW数据集
    
    参数:
    - data_home: 数据根目录
    - min_faces_per_person: 每个人最少的图片数量
    - resize: 图像缩放比例
    
    返回:
    - Bunch对象，包含images, data, target, target_names等属性
    """
    
    lfw_funneled_path = os.path.join(data_home, "lfw_home", "lfw_funneled")
    
    if not os.path.exists(lfw_funneled_path):
        raise FileNotFoundError(f"LFW数据路径不存在: {lfw_funneled_path}")
    
    # 获取所有人物文件夹
    person_dirs = [d for d in os.listdir(lfw_funneled_path) 
                   if os.path.isdir(os.path.join(lfw_funneled_path, d))]
    
    # 统计每个人的图片数量并过滤
    valid_persons = []
    person_image_paths = {}
    
    for person_dir in person_dirs:
        person_path = os.path.join(lfw_funneled_path, person_dir)
        image_files = [f for f in os.listdir(person_path) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        
        if len(image_files) >= min_faces_per_person:
            valid_persons.append(person_dir)
            person_image_paths[person_dir] = [
                os.path.join(person_path, img) for img in image_files
            ]
    
    print(f"找到 {len(valid_persons)} 个有效人物")
    
    # 加载图像数据
    images = []
    targets = []
    target_names = sorted(valid_persons)
    
    for person_idx, person_name in enumerate(target_names):
        for img_path in person_image_paths[person_name]:
            try:
                # 加载并处理图像
                img = Image.open(img_path).convert('L')  # 转为灰度图
                
                # 调整大小
                if resize != 1.0:
                    new_size = (int(img.width * resize), int(img.height * resize))
                    img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # 转为numpy数组
                img_array = np.array(img, dtype=np.float32) / 255.0
                
                images.append(img_array)
                targets.append(person_idx)
                
            except Exception as e:
                print(f"跳过损坏的图像: {img_path}, 错误: {e}")
                continue
    
    # 转换为numpy数组
    images = np.array(images)
    targets = np.array(targets)
    
    # 创建data数组（展平的图像）
    n_samples, h, w = images.shape
    data = images.reshape(n_samples, h * w)
    
    print(f"加载完成: {n_samples} 个样本, {len(target_names)} 个类别")
    
    return Bunch(
        images=images,
        data=data,
        target=targets,
        target_names=np.array(target_names),
        DESCR="Local LFW dataset"
    )

# 使用示例
if __name__ == "__main__":
    lfw_people = load_local_lfw_people()
    print(f"样本数量: {lfw_people.images.shape[0]}")
    print(f"图像尺寸: {lfw_people.images.shape[1:3]}")
    print(f"类别数量: {len(lfw_people.target_names)}")
